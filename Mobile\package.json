{"name": "SocialSyncMobile", "version": "1.0.0", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "start": "react-native start", "test": "jest", "test:e2e": "detox test", "test:e2e:build": "detox build", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "type-check": "tsc --noEmit", "build:android": "cd android && ./gradlew assembleRelease", "build:ios": "xcodebuild -workspace ios/SocialSyncMobile.xcworkspace -scheme SocialSyncMobile -configuration Release -destination generic/platform=iOS -archivePath ios/build/SocialSyncMobile.xcarchive archive", "postinstall": "cd ios && pod install"}, "dependencies": {"react": "18.2.0", "react-native": "0.72.0", "@react-navigation/native": "^6.1.0", "@react-navigation/stack": "^6.3.0", "@react-navigation/bottom-tabs": "^6.5.0", "@react-navigation/drawer": "^6.6.0", "@reduxjs/toolkit": "^1.9.0", "react-redux": "^8.1.0", "react-native-keychain": "^8.1.0", "react-native-touch-id": "^4.4.1", "react-native-image-picker": "^5.6.0", "react-native-camera": "^4.2.1", "react-native-elements": "^3.4.3", "react-native-vector-icons": "^10.0.0", "react-native-safe-area-context": "^4.7.0", "react-native-screens": "^3.22.0", "react-native-gesture-handler": "^2.12.0", "react-native-reanimated": "^3.3.0", "@react-native-firebase/app": "^18.0.0", "@react-native-firebase/messaging": "^18.0.0", "@react-native-firebase/crashlytics": "^18.0.0", "@react-native-firebase/analytics": "^18.0.0", "@react-native-async-storage/async-storage": "^1.19.0", "react-native-permissions": "^3.8.0", "react-native-share": "^9.4.0", "react-native-device-info": "^10.8.0", "react-native-network-info": "^5.2.1", "react-native-haptic-feedback": "^2.2.0", "react-native-fast-image": "^8.6.3", "react-native-video": "^5.2.1", "react-native-svg": "^13.9.0", "react-native-linear-gradient": "^2.8.0", "react-native-modal": "^13.0.1", "react-native-date-picker": "^4.2.13", "react-native-chart-kit": "^6.12.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/eslint-config": "^0.72.0", "@react-native/metro-config": "^0.72.0", "@tsconfig/react-native": "^3.0.0", "@types/react": "^18.0.24", "@types/react-test-renderer": "^18.0.0", "@types/jest": "^29.2.1", "babel-jest": "^29.2.1", "eslint": "^8.19.0", "jest": "^29.2.1", "metro-react-native-babel-preset": "0.76.5", "prettier": "^2.4.1", "react-test-renderer": "18.2.0", "typescript": "4.8.4", "@testing-library/react-native": "^12.1.2", "@testing-library/jest-native": "^5.4.2", "detox": "^20.7.0", "flipper-plugin-react-query-native-devtools": "^4.0.0"}, "jest": {"preset": "react-native", "setupFilesAfterEnv": ["@testing-library/jest-native/extend-expect"], "transformIgnorePatterns": ["node_modules/(?!(react-native|@react-native|react-native-vector-icons|react-native-elements|@react-navigation)/)"], "collectCoverageFrom": ["src/**/*.{js,jsx,ts,tsx}", "!src/**/*.d.ts", "!src/types/**/*"], "coverageThreshold": {"global": {"branches": 80, "functions": 80, "lines": 80, "statements": 80}}}, "detox": {"test-runner": "jest", "runner-config": "__tests__/detox.config.js", "configurations": {"ios.sim.debug": {"device": "simulator", "app": "ios.debug"}, "ios.sim.release": {"device": "simulator", "app": "ios.release"}, "android.emu.debug": {"device": "emulator", "app": "android.debug"}, "android.emu.release": {"device": "emulator", "app": "android.release"}}, "devices": {"simulator": {"type": "ios.simulator", "device": {"type": "iPhone 14"}}, "emulator": {"type": "android.emulator", "device": {"avdName": "Pixel_4_API_30"}}}, "apps": {"ios.debug": {"type": "ios.app", "binaryPath": "ios/build/Build/Products/Debug-iphonesimulator/SocialSyncMobile.app", "build": "xcodebuild -workspace ios/SocialSyncMobile.xcworkspace -scheme SocialSyncMobile -configuration Debug -sdk iphonesimulator -derivedDataPath ios/build"}, "ios.release": {"type": "ios.app", "binaryPath": "ios/build/Build/Products/Release-iphonesimulator/SocialSyncMobile.app", "build": "xcodebuild -workspace ios/SocialSyncMobile.xcworkspace -scheme SocialSyncMobile -configuration Release -sdk iphonesimulator -derivedDataPath ios/build"}, "android.debug": {"type": "android.apk", "binaryPath": "android/app/build/outputs/apk/debug/app-debug.apk", "build": "cd android && ./gradlew assembleDebug assembleAndroidTest -DtestBuildType=debug"}, "android.release": {"type": "android.apk", "binaryPath": "android/app/build/outputs/apk/release/app-release.apk", "build": "cd android && ./gradlew assembleRelease assembleAndroidTest -DtestBuildType=release"}}}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}