{"extends": ["next/core-web-vitals", "eslint:recommended", "@typescript-eslint/recommended"], "parser": "@typescript-eslint/parser", "plugins": ["@typescript-eslint"], "parserOptions": {"ecmaVersion": 2021, "sourceType": "module", "ecmaFeatures": {"jsx": true}}, "rules": {"@next/next/no-img-element": "off", "@typescript-eslint/no-unused-vars": ["error", {"argsIgnorePattern": "^_"}], "@typescript-eslint/no-explicit-any": "warn", "@typescript-eslint/explicit-function-return-type": "off", "@typescript-eslint/explicit-module-boundary-types": "off", "@typescript-eslint/no-empty-function": "warn", "prefer-const": "error", "no-var": "error", "no-console": ["warn", {"allow": ["warn", "error"]}], "eqeqeq": ["error", "always"], "curly": ["error", "all"], "brace-style": ["error", "1tbs"], "comma-dangle": ["error", "es5"], "quotes": ["error", "single", {"avoidEscape": true}], "semi": ["error", "always"], "indent": ["error", 2, {"SwitchCase": 1}], "max-len": ["warn", {"code": 100, "ignoreUrls": true}], "object-curly-spacing": ["error", "always"], "array-bracket-spacing": ["error", "never"]}, "env": {"browser": true, "es2021": true, "node": true}}