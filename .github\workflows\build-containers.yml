---
name: "Build Containers"

on:
  workflow_dispatch:
  push:
    tags:
      - '*'

jobs:
  build-containers-common:
    runs-on: ubuntu-latest
    outputs:
      containerver: ${{ steps.getcontainerver.outputs.containerver }}
    steps:
      - name: Get Container Version
        id: getcontainerver
        run: |
          echo "containerver=${{ github.ref_name }}" >> "$GITHUB_OUTPUT"

  build-containers:
    needs: build-containers-common
    strategy:
      matrix:
        include:
          - runnertags: ubuntu-latest
            arch: amd64
          - runnertags: ubuntu-24.04-arm
            arch: arm64
    runs-on: ${{ matrix.runnertags }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Login to ghcr
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ github.token }}

      - name: Build and Push Image
        env:
          CONTAINERVER: ${{ needs.build-containers-common.outputs.containerver }}
          NEXT_PUBLIC_VERSION: ${{ github.ref_name }}
        run: |
          docker buildx build --platform linux/${{ matrix.arch }} \
            -f Dockerfile.dev \
            -t ghcr.io/gitroomhq/postiz-app:${{ env.CONTAINERVER }}-${{ matrix.arch }} \
            --build-arg NEXT_PUBLIC_VERSION=${{ env.NEXT_PUBLIC_VERSION }} \
            --provenance=false --sbom=false \
            --output "type=registry,name=ghcr.io/gitroomhq/postiz-app:${{ env.CONTAINERVER }}-${{ matrix.arch }}" .

  build-container-manifest:
    needs: [build-containers, build-containers-common]
    runs-on: ubuntu-latest
    steps:
      - name: Login to ghcr
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ github.token }}

      - name: Create Docker Manifest
        env:
          CONTAINERVER: ${{ needs.build-containers-common.outputs.containerver }}
        run: |
          # Verify the architecture images
          echo "Verifying AMD64 image:"
          docker buildx imagetools inspect ghcr.io/gitroomhq/postiz-app:${{ env.CONTAINERVER }}-amd64
          
          echo "Verifying ARM64 image:"
          docker buildx imagetools inspect ghcr.io/gitroomhq/postiz-app:${{ env.CONTAINERVER }}-arm64
          
          # Try to remove any existing manifests first
          docker manifest rm ghcr.io/gitroomhq/postiz-app:${{ env.CONTAINERVER }} || true
          docker manifest rm ghcr.io/gitroomhq/postiz-app:latest || true
          
          # Create and push the version-specific manifest
          docker manifest create ghcr.io/gitroomhq/postiz-app:${{ env.CONTAINERVER }} \
            --amend ghcr.io/gitroomhq/postiz-app:${{ env.CONTAINERVER }}-amd64 \
            --amend ghcr.io/gitroomhq/postiz-app:${{ env.CONTAINERVER }}-arm64

          docker manifest push ghcr.io/gitroomhq/postiz-app:${{ env.CONTAINERVER }}

          # Create and push the latest manifest
          docker manifest create ghcr.io/gitroomhq/postiz-app:latest \
            --amend ghcr.io/gitroomhq/postiz-app:${{ env.CONTAINERVER }}-amd64 \
            --amend ghcr.io/gitroomhq/postiz-app:${{ env.CONTAINERVER }}-arm64

          docker manifest push ghcr.io/gitroomhq/postiz-app:latest

      - name: Verify Manifest
        run: |
          docker manifest inspect ghcr.io/gitroomhq/postiz-app:latest
