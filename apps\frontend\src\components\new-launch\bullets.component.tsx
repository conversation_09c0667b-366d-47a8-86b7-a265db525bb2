'use client';

import { FC, useCallback } from 'react';

export const Bullets: FC<{
  editor: any;
  currentValue: string;
}> = ({ editor }) => {
  const bullet = () => {
    editor?.commands?.toggleBulletList();
  };
  return (
    <div
      onClick={bullet}
      className="select-none cursor-pointer w-[40px] p-[5px] text-center"
    >
      <svg
        width="20"
        height="14"
        viewBox="0 0 26 20"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M6.5 2C6.5 1.60218 6.65804 1.22064 6.93934 0.93934C7.22064 0.658035 7.60218 0.5 8 0.5H24C24.3978 0.5 24.7794 0.658035 25.0607 0.93934C25.342 1.22064 25.5 1.60218 25.5 2C25.5 2.39782 25.342 2.77936 25.0607 3.06066C24.7794 3.34196 24.3978 3.5 24 3.5H8C7.60218 3.5 7.22064 3.34196 6.93934 3.06066C6.65804 2.77936 6.5 2.39782 6.5 2ZM24 8.5H8C7.60218 8.5 7.22064 8.65804 6.93934 8.93934C6.65804 9.22064 6.5 9.60218 6.5 10C6.5 10.3978 6.65804 10.7794 6.93934 11.0607C7.22064 11.342 7.60218 11.5 8 11.5H24C24.3978 11.5 24.7794 11.342 25.0607 11.0607C25.342 10.7794 25.5 10.3978 25.5 10C25.5 9.60218 25.342 9.22064 25.0607 8.93934C24.7794 8.65804 24.3978 8.5 24 8.5ZM24 16.5H8C7.60218 16.5 7.22064 16.658 6.93934 16.9393C6.65804 17.2206 6.5 17.6022 6.5 18C6.5 18.3978 6.65804 18.7794 6.93934 19.0607C7.22064 19.342 7.60218 19.5 8 19.5H24C24.3978 19.5 24.7794 19.342 25.0607 19.0607C25.342 18.7794 25.5 18.3978 25.5 18C25.5 17.6022 25.342 17.2206 25.0607 16.9393C24.7794 16.658 24.3978 16.5 24 16.5ZM2.5 8C2.10444 8 1.71776 8.1173 1.38886 8.33706C1.05996 8.55682 0.803617 8.86918 0.652242 9.23463C0.500867 9.60009 0.46126 10.0022 0.53843 10.3902C0.615601 10.7781 0.806082 11.1345 1.08579 11.4142C1.36549 11.6939 1.72186 11.8844 2.10982 11.9616C2.49778 12.0387 2.89992 11.9991 3.26537 11.8478C3.63082 11.6964 3.94318 11.44 4.16294 11.1111C4.3827 10.7822 4.5 10.3956 4.5 10C4.5 9.46957 4.28929 8.96086 3.91421 8.58579C3.53914 8.21071 3.03043 8 2.5 8ZM2.5 0C2.10444 0 1.71776 0.117298 1.38886 0.337061C1.05996 0.556824 0.803617 0.869181 0.652242 1.23463C0.500867 1.60009 0.46126 2.00222 0.53843 2.39018C0.615601 2.77814 0.806082 3.13451 1.08579 3.41421C1.36549 3.69392 1.72186 3.8844 2.10982 3.96157C2.49778 4.03874 2.89992 3.99913 3.26537 3.84776C3.63082 3.69638 3.94318 3.44004 4.16294 3.11114C4.3827 2.78224 4.5 2.39556 4.5 2C4.5 1.46957 4.28929 0.960859 3.91421 0.585786C3.53914 0.210714 3.03043 0 2.5 0ZM2.5 16C2.10444 16 1.71776 16.1173 1.38886 16.3371C1.05996 16.5568 0.803617 16.8692 0.652242 17.2346C0.500867 17.6001 0.46126 18.0022 0.53843 18.3902C0.615601 18.7781 0.806082 19.1345 1.08579 19.4142C1.36549 19.6939 1.72186 19.8844 2.10982 19.9616C2.49778 20.0387 2.89992 19.9991 3.26537 19.8478C3.63082 19.6964 3.94318 19.44 4.16294 19.1111C4.3827 18.7822 4.5 18.3956 4.5 18C4.5 17.4696 4.28929 16.9609 3.91421 16.5858C3.53914 16.2107 3.03043 16 2.5 16Z"
          fill="currentColor"
        />
      </svg>
    </div>
  );
};
