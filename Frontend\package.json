{"name": "socialsync-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start -p 3000", "lint": "next lint", "prepare": "husky"}, "dependencies": {"@emotion/cache": "^11.11.0", "@emotion/react": "^11.11.1", "@emotion/server": "^11.11.0", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.15.20", "@mui/material": "^5.15.20", "axios": "^1.7.2", "formik": "^2.4.6", "next": "^14.2.5", "react": "^18.3.1", "react-dom": "^18.3.1", "react-query": "^3.39.3", "yup": "^1.4.0"}, "devDependencies": {"@types/node": "^20.12.11", "@types/react": "^18.2.79", "@types/react-dom": "^18.2.25", "eslint": "^8.57.0", "eslint-config-next": "^14.2.5", "husky": "^9.0.11", "lint-staged": "^15.2.7", "prettier": "^3.3.2", "typescript": "^5.5.4"}}