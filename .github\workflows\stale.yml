name: Close inactive issues

on:
  workflow_dispatch:
  schedule:
    - cron: "*/30 * * * *"

jobs:
  close-issues:
    if: github.repository == 'gitroomhq/postiz-app'
    runs-on: ubuntu-latest
    permissions:
      issues: write
      pull-requests: write
    steps:
      - uses: actions/stale@v9
        with:
          days-before-issue-stale: 90
          days-before-issue-close: 7
          stale-issue-label: "stale"
          stale-issue-message: "This issue is stale because it has been open for 90 days with no activity."
          close-issue-message: "This issue was closed because it has been inactive for 7 days since being marked as stale." 
          exempt-issue-labels: "no-stale-bot"
          
          days-before-pr-stale: 90
          days-before-pr-close: 7
          stale-pr-label: "stale" 
          stale-pr-message: "This PR is stale because it has been open for 90 days with no activity."
          close-pr-message: "This PR was closed because it has been inactive for 7 days since being marked as stale." 
          exempt-pr-label: "no-stale-bot" 
          
          repo-token: ${{ secrets.GITHUB_TOKEN }}
          operations-per-run: 180
