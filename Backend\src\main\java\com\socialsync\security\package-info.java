/**
 * Security configuration and components for the SocialSync application.
 * 
 * This package contains all security-related classes including:
 * - Spring Security configuration
 * - JWT token utilities
 * - Authentication providers
 * - Authorization handlers
 * - Security filters
 * - OAuth2 configuration
 * 
 * Security components handle authentication, authorization,
 * and protection of API endpoints.
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
package com.socialsync.security;
