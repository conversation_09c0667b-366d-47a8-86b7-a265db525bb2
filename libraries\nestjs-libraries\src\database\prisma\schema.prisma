// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Organization {
  id                String             @id @default(uuid())
  name              String
  description       String?
  apiKey            String?
  users             UserOrganization[]
  media             Media[]
  paymentId         String?
  createdAt         DateTime           @default(now())
  updatedAt         DateTime           @updatedAt
  github            GitHub[]
  subscription      Subscription?
  Integration       Integration[]
  post              Post[]             @relation("organization")
  submittedPost     Post[]             @relation("submittedForOrg")
  allowTrial        Boolean            @default(false)
  isTrailing        Boolean            @default(false)
  Comments          Comments[]
  notifications     Notifications[]
  buyerOrganization MessagesGroup[]
  usedCodes         UsedCodes[]
  credits           Credits[]
  plugs             Plugs[]
  customers         Customer[]
  webhooks          Webhooks[]
  tags              Tags[]
  signatures        Signatures[]
  autoPost          AutoPost[]
  sets              Sets[]
  thirdParty        ThirdParty[]
  errors            Errors[]
}

model Tags {
  id           String       @id @default(uuid())
  name         String
  color        String
  orgId        String
  organization Organization @relation(fields: [orgId], references: [id])
  posts        TagsPosts[]
  deletedAt    DateTime?
  createdAt    DateTime     @default(now())
  updatedAt    DateTime     @updatedAt

  @@index([orgId])
  @@index([deletedAt])
}

model TagsPosts {
  postId    String
  post      Post     @relation(fields: [postId], references: [id])
  tagId     String
  tag       Tags     @relation(fields: [tagId], references: [id])
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@id([postId, tagId])
  @@unique([postId, tagId])
}

model User {
  id                    String              @id @default(uuid())
  email                 String
  password              String?
  providerName          Provider
  name                  String?
  lastName              String?
  isSuperAdmin          Boolean             @default(false)
  bio                   String?
  audience              Int                 @default(0)
  pictureId             String?
  picture               Media?              @relation(fields: [pictureId], references: [id])
  providerId            String?
  organizations         UserOrganization[]
  timezone              Int
  comments              Comments[]
  createdAt             DateTime            @default(now())
  updatedAt             DateTime            @updatedAt
  lastReadNotifications DateTime            @default(now())
  inviteId              String?
  activated             Boolean             @default(true)
  items                 ItemUser[]
  marketplace           Boolean             @default(true)
  account               String?
  connectedAccount      Boolean             @default(false)
  groupBuyer            MessagesGroup[]     @relation("groupBuyer")
  groupSeller           MessagesGroup[]     @relation("groupSeller")
  orderBuyer            Orders[]            @relation("orderBuyer")
  orderSeller           Orders[]            @relation("orderSeller")
  payoutProblems        PayoutProblems[]
  lastOnline            DateTime            @default(now())
  agencies              SocialMediaAgency[]
  ip                    String?
  agent                 String?

  @@unique([email, providerName])
  @@index([lastReadNotifications])
  @@index([inviteId])
  @@index([account])
  @@index([lastOnline])
  @@index([pictureId])
}

model UsedCodes {
  id           String       @id @default(uuid())
  code         String
  orgId        String
  organization Organization @relation(fields: [orgId], references: [id])
  createdAt    DateTime     @default(now())
  updatedAt    DateTime     @updatedAt

  @@index([code])
}

model UserOrganization {
  id             String       @id @default(uuid())
  user           User         @relation(fields: [userId], references: [id])
  userId         String
  organization   Organization @relation(fields: [organizationId], references: [id])
  organizationId String
  disabled       Boolean      @default(false)
  role           Role         @default(USER)
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt

  @@unique([userId, organizationId])
  @@index([disabled])
}

model GitHub {
  id             String       @id @default(uuid())
  login          String?
  name           String?
  token          String
  jobId          String?
  organization   Organization @relation(fields: [organizationId], references: [id])
  organizationId String
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt

  @@index([login])
  @@index([organizationId])
}

model Trending {
  id           String   @id @default(uuid())
  trendingList String
  language     String?
  hash         String
  date         DateTime
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  @@unique([language])
  @@index([hash])
}

model TrendingLog {
  id       String   @id @default(uuid())
  language String?
  date     DateTime
}

model ItemUser {
  id     String @id @default(uuid())
  user   User   @relation(fields: [userId], references: [id])
  userId String
  key    String

  @@unique([userId, key])
  @@index([userId])
  @@index([key])
}

model Star {
  id         String   @id @default(uuid())
  stars      Int
  totalStars Int
  forks      Int
  totalForks Int
  login      String
  date       DateTime @default(now()) @db.Date
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  @@unique([login, date])
}

model Media {
  id                 String              @id @default(uuid())
  name               String
  path               String
  organization       Organization        @relation(fields: [organizationId], references: [id])
  organizationId     String
  thumbnail          String?
  thumbnailTimestamp Int?
  alt                String?
  fileSize           Int                 @default(0)
  type               String              @default("image")
  createdAt          DateTime            @default(now())
  updatedAt          DateTime            @updatedAt
  userPicture        User[]
  agencies           SocialMediaAgency[]
  deletedAt          DateTime?

  @@index([name])
  @@index([organizationId])
  @@index([type])
}

model SocialMediaAgency {
  id      String  @id @default(uuid())
  user    User    @relation(fields: [userId], references: [id])
  userId  String  @unique()
  name    String
  logoId  String?
  logo    Media?  @relation(fields: [logoId], references: [id])
  website String?
  slug    String?

  facebook         String?
  instagram        String?
  twitter          String?
  linkedIn         String?
  youtube          String?
  tiktok           String?
  otherSocialMedia String?

  shortDescription String
  description      String
  niches           SocialMediaAgencyNiche[]
  approved         Boolean                  @default(false)

  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  deletedAt DateTime?

  @@index([userId])
  @@index([deletedAt])
  @@index([id])
}

model SocialMediaAgencyNiche {
  agencyId String
  agency   SocialMediaAgency @relation(fields: [agencyId], references: [id])
  niche    String

  @@id([agencyId, niche])
}

model Credits {
  id             String       @id @default(uuid())
  organization   Organization @relation(fields: [organizationId], references: [id])
  organizationId String
  credits        Int
  type           String       @default("ai_images")
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt

  @@index([organizationId])
  @@index([createdAt])
}

model Subscription {
  id               String           @id @default(cuid())
  organizationId   String           @unique
  organization     Organization     @relation(fields: [organizationId], references: [id])
  subscriptionTier SubscriptionTier
  identifier       String?
  cancelAt         DateTime?
  period           Period
  totalChannels    Int
  isLifetime       Boolean          @default(false)
  createdAt        DateTime         @default(now())
  updatedAt        DateTime         @updatedAt
  deletedAt        DateTime?

  @@index([organizationId])
  @@index([deletedAt])
}

model Customer {
  id           String        @id @default(uuid())
  name         String
  orgId        String
  organization Organization  @relation(fields: [orgId], references: [id])
  integrations Integration[]
  createdAt    DateTime      @default(now())
  updatedAt    DateTime      @updatedAt
  deletedAt    DateTime?

  @@unique([orgId, name, deletedAt])
}

model Integration {
  id                    String                 @id @default(cuid())
  internalId            String
  organizationId        String
  name                  String
  organization          Organization           @relation(fields: [organizationId], references: [id])
  picture               String?
  providerIdentifier    String
  type                  String
  token                 String
  disabled              Boolean                @default(false)
  tokenExpiration       DateTime?
  refreshToken          String?
  posts                 Post[]
  profile               String?
  deletedAt             DateTime?
  createdAt             DateTime               @default(now())
  updatedAt             DateTime?              @updatedAt
  orderItems            OrderItems[]
  inBetweenSteps        Boolean                @default(false)
  refreshNeeded         Boolean                @default(false)
  postingTimes          String                 @default("[{\"time\":120}, {\"time\":400}, {\"time\":700}]")
  customInstanceDetails String?
  customerId            String?
  customer              Customer?              @relation(fields: [customerId], references: [id])
  plugs                 Plugs[]
  exisingPlugData       ExisingPlugData[]
  rootInternalId        String?
  additionalSettings    String?                @default("[]")
  webhooks              IntegrationsWebhooks[]

  @@unique([organizationId, internalId])
  @@index([rootInternalId])
  @@index([organizationId])
  @@index([providerIdentifier])
  @@index([updatedAt])
  @@index([createdAt])
  @@index([deletedAt])
  @@index([customerId])
  @@index([inBetweenSteps])
  @@index([refreshNeeded])
  @@index([disabled])
}

model Signatures {
  id             String       @id @default(uuid())
  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id])
  content        String
  autoAdd        Boolean
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt
  deletedAt      DateTime?

  @@index([createdAt])
  @@index([organizationId])
  @@index([deletedAt])
}

model Comments {
  id             String       @id @default(uuid())
  content        String
  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id])
  postId         String
  post           Post         @relation(fields: [postId], references: [id])
  userId         String
  user           User         @relation(fields: [userId], references: [id])
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt
  deletedAt      DateTime?

  @@index([createdAt])
  @@index([organizationId])
  @@index([userId])
  @@index([postId])
  @@index([deletedAt])
}

model Post {
  id                         String                    @id @default(cuid())
  state                      State                     @default(QUEUE)
  publishDate                DateTime
  organizationId             String
  integrationId              String
  content                    String
  group                      String
  organization               Organization              @relation("organization", fields: [organizationId], references: [id])
  integration                Integration               @relation(fields: [integrationId], references: [id])
  title                      String?
  description                String?
  parentPostId               String?
  releaseId                  String?
  releaseURL                 String?
  settings                   String?
  parentPost                 Post?                     @relation("parentPostId", fields: [parentPostId], references: [id])
  childrenPost               Post[]                    @relation("parentPostId")
  image                      String?
  submittedForOrderId        String?
  submittedForOrder          Orders?                   @relation(fields: [submittedForOrderId], references: [id])
  submittedForOrganizationId String?
  submittedForOrganization   Organization?             @relation("submittedForOrg", fields: [submittedForOrganizationId], references: [id])
  approvedSubmitForOrder     APPROVED_SUBMIT_FOR_ORDER @default(NO)
  lastMessageId              String?
  lastMessage                Messages?                 @relation(fields: [lastMessageId], references: [id])
  intervalInDays             Int?
  payoutProblems             PayoutProblems[]
  comments                   Comments[]
  tags                       TagsPosts[]
  errors                     Errors[]
  error                      String?
  createdAt                  DateTime                  @default(now())
  updatedAt                  DateTime                  @updatedAt
  deletedAt                  DateTime?

  @@index([group])
  @@index([deletedAt])
  @@index([publishDate])
  @@index([state])
  @@index([organizationId])
  @@index([parentPostId])
  @@index([submittedForOrderId])
  @@index([intervalInDays])
  @@index([approvedSubmitForOrder])
  @@index([lastMessageId])
  @@index([createdAt])
  @@index([updatedAt])
  @@index([releaseURL])
  @@index([integrationId])
}

model Notifications {
  id             String       @id @default(uuid())
  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id])
  content        String
  link           String?
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt
  deletedAt      DateTime?

  @@index([createdAt])
  @@index([organizationId])
  @@index([deletedAt])
}

model MessagesGroup {
  id                  String       @id @default(uuid())
  buyerOrganizationId String
  buyerOrganization   Organization @relation(fields: [buyerOrganizationId], references: [id])
  buyerId             String
  buyer               User         @relation("groupBuyer", fields: [buyerId], references: [id])
  sellerId            String
  seller              User         @relation("groupSeller", fields: [sellerId], references: [id])
  messages            Messages[]
  orders              Orders[]
  createdAt           DateTime     @default(now())
  updatedAt           DateTime     @updatedAt

  @@unique([buyerId, sellerId])
  @@index([createdAt])
  @@index([updatedAt])
  @@index([buyerOrganizationId])
}

model PayoutProblems {
  id        String   @id @default(uuid())
  status    String
  orderId   String
  order     Orders   @relation(fields: [orderId], references: [id])
  userId    String
  user      User     @relation(fields: [userId], references: [id])
  postId    String?
  post      Post?    @relation(fields: [postId], references: [id])
  amount    Int
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model Orders {
  id             String           @id @default(uuid())
  buyerId        String
  sellerId       String
  posts          Post[]
  buyer          User             @relation("orderBuyer", fields: [buyerId], references: [id])
  seller         User             @relation("orderSeller", fields: [sellerId], references: [id])
  status         OrderStatus
  ordersItems    OrderItems[]
  messageGroupId String
  messageGroup   MessagesGroup    @relation(fields: [messageGroupId], references: [id])
  captureId      String?
  payoutProblems PayoutProblems[]
  createdAt      DateTime         @default(now())
  updatedAt      DateTime         @updatedAt

  @@index([buyerId])
  @@index([sellerId])
  @@index([updatedAt])
  @@index([createdAt])
  @@index([messageGroupId])
}

model OrderItems {
  id            String      @id @default(uuid())
  orderId       String
  order         Orders      @relation(fields: [orderId], references: [id])
  integrationId String
  integration   Integration @relation(fields: [integrationId], references: [id])
  quantity      Int
  price         Int

  @@index([orderId])
  @@index([integrationId])
}

model Messages {
  id        String        @id @default(uuid())
  from      From
  content   String?
  groupId   String
  group     MessagesGroup @relation(fields: [groupId], references: [id])
  special   String?
  posts     Post[]
  createdAt DateTime      @default(now())
  updatedAt DateTime      @updatedAt
  deletedAt DateTime?

  @@index([groupId])
  @@index([createdAt])
  @@index([deletedAt])
}

model Plugs {
  id             String       @id @default(uuid())
  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id])
  plugFunction   String
  data           String
  integrationId  String
  integration    Integration  @relation(fields: [integrationId], references: [id])
  activated      Boolean      @default(true)

  @@unique([plugFunction, integrationId])
  @@index([organizationId])
}

model ExisingPlugData {
  id            String      @id @default(uuid())
  integrationId String
  integration   Integration @relation(fields: [integrationId], references: [id])
  methodName    String
  value         String

  @@unique([integrationId, methodName, value])
}

model PopularPosts {
  id        String   @id @default(uuid())
  category  String
  topic     String
  content   String
  hook      String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model IntegrationsWebhooks {
  integrationId String
  integration   Integration @relation(fields: [integrationId], references: [id])
  webhookId     String
  webhook       Webhooks    @relation(fields: [webhookId], references: [id])

  @@id([integrationId, webhookId])
  @@unique([integrationId, webhookId])
  @@index([integrationId])
  @@index([webhookId])
}

model Webhooks {
  id             String                 @id @default(uuid())
  name           String
  organizationId String
  organization   Organization           @relation(fields: [organizationId], references: [id])
  integrations   IntegrationsWebhooks[]
  url            String
  deletedAt      DateTime?
  createdAt      DateTime               @default(now())
  updatedAt      DateTime               @updatedAt

  @@index([organizationId])
  @@index([deletedAt])
}

model AutoPost {
  id              String       @id @default(uuid())
  organizationId  String
  organization    Organization @relation(fields: [organizationId], references: [id])
  title           String
  content         String?
  onSlot          Boolean
  syncLast        Boolean
  url             String
  lastUrl         String
  active          Boolean
  addPicture      Boolean
  generateContent Boolean
  integrations    String
  deletedAt       DateTime?
  createdAt       DateTime     @default(now())
  updatedAt       DateTime     @updatedAt

  @@index([deletedAt])
}

model Sets {
  id             String       @id @default(uuid())
  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id])
  name           String
  content        String
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt

  @@index([organizationId])
}

model ThirdParty {
  id             String       @id @default(uuid())
  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id])
  identifier     String
  name           String
  internalId     String
  apiKey         String
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt
  deletedAt      DateTime?

  @@unique([organizationId, internalId])
  @@index([organizationId])
  @@index([deletedAt])
}

model Errors {
  id             String       @id @default(uuid())
  message        String
  body           String       @default("{}")
  platform       String
  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id])
  postId         String
  post           Post         @relation(fields: [postId], references: [id])
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt

  @@index([organizationId])
  @@index([createdAt])
}

model Mentions {
  name      String
  username  String
  platform  String
  image     String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@id([name, username, platform, image])
  @@index([createdAt])
}

enum OrderStatus {
  PENDING
  ACCEPTED
  CANCELED
  COMPLETED
}

enum From {
  BUYER
  SELLER
}

enum State {
  QUEUE
  PUBLISHED
  ERROR
  DRAFT
}

enum SubscriptionTier {
  STANDARD
  PRO
  TEAM
  ULTIMATE
}

enum Period {
  MONTHLY
  YEARLY
}

enum Provider {
  LOCAL
  GITHUB
  GOOGLE
  FARCASTER
  WALLET
  GENERIC
}

enum Role {
  SUPERADMIN
  ADMIN
  USER
}

enum APPROVED_SUBMIT_FOR_ORDER {
  NO
  WAITING_CONFIRMATION
  YES
}
