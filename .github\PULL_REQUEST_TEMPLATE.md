# What kind of change does this PR introduce?

eg: Bug fix, feature, docs update, ...

# Why was this change needed?

Please link to related issues when possible, and explain WHY you changed things, not WHAT you changed.

# Other information:

eg: Did you discuss this change with anybody before working on it (not required, but can be a good idea for bigger changes). Any plans for the future, etc?

# Checklist:

Put a "X" in the boxes below to indicate you have followed the checklist;

- [ ] I have read the [CONTRIBUTING](https://github.com/gitroomhq/postiz-app/blob/main/CONTRIBUTING.md) guide.
- [ ] I checked that there were not similar issues or PRs already open for this.
- [ ] This PR fixes just ONE issue (do not include multiple issues or types of change in the same PR) For example, don't try and fix a UI issue and include new dependencies in the same PR.
