{"extends": "../../tsconfig.base.json", "compilerOptions": {"target": "esnext", "types": ["vite/client", "node", "chrome"], "lib": ["dom", "dom.iterable", "esnext"], "allowJs": false, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "noEmit": true, "jsx": "react-jsx"}, "include": ["src", "utils", "vite.config.base.ts", "vite.config.chrome.ts", "vite.config.firefox.ts"]}