# Do **not** use this yml for production. It is not up-to-date.
# Use https://docs.postiz.com/installation/docker-compose
# This is only for the dev enviroment

services:
  postiz-postgres:
    # ref: https://hub.docker.com/_/postgres
    image: postgres:17-alpine # 17.0
    container_name: postiz-postgres
    restart: always
    environment:
      POSTGRES_PASSWORD: postiz-local-pwd
      POSTGRES_USER: postiz-local
      POSTGRES_DB: postiz-db-local
    volumes:
      - postgres-volume:/var/lib/postgresql/data
    ports:
      - 5432:5432
    networks:
      - postiz-network
  postiz-redis:
    # ref: https://hub.docker.com/_/redis
    image: redis:7-alpine # 7.4.0
    container_name: postiz-redis
    restart: always
    ports:
      - 6379:6379
    networks:
      - postiz-network
  postiz-pg-admin:
    # ref: https://hub.docker.com/r/dpage/pgadmin4/tags
    image: dpage/pgadmin4:latest
    container_name: postiz-pg-admin
    restart: always
    ports:
      - 8081:80
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin
    networks:
      - postiz-network
  postiz-redisinsight:
    # ref: https://hub.docker.com/r/redis/redisinsight
    image: redis/redisinsight:latest
    container_name: postiz-redisinsight
    links:
      - postiz-redis
    ports:
      - '5540:5540'
    volumes:
      - redisinsight:/data
    networks:
      - postiz-network
    restart: always

volumes:
  redisinsight:
  postgres-volume:
    external: false

networks:
  postiz-network:
    external: false
