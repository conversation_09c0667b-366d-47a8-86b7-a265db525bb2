# Redis Configuration for SocialSync Development Environment
# This configuration is optimized for development use

# Network and Security
bind 0.0.0.0
port 6379
protected-mode yes

# Memory Management
maxmemory 256mb
maxmemory-policy allkeys-lru

# Persistence
save 900 1
save 300 10
save 60 10000

# Enable AOF for better durability
appendonly yes
appendfsync everysec

# Logging
loglevel notice
logfile ""

# Performance
tcp-keepalive 300
timeout 0

# Database
databases 16

# Keyspace notifications (useful for session management)
notify-keyspace-events Ex

# Slow log
slowlog-log-slower-than 10000
slowlog-max-len 128

# Client output buffer limits
client-output-buffer-limit normal 0 0 0
client-output-buffer-limit replica 256mb 64mb 60
client-output-buffer-limit pubsub 32mb 8mb 60

# Development-specific settings
# Disable some security features for easier development
# (These should be enabled in production)
stop-writes-on-bgsave-error no
