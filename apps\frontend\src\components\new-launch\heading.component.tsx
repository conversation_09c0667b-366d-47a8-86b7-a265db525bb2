'use client';

import { FC, useCallback } from 'react';

export const HeadingComponent: FC<{
  editor: any;
  currentValue: string;
}> = ({ editor }) => {
  const setHeading = (level: number) => () => {
    editor?.commands?.unsetUnderline();
    editor?.commands?.unsetBold();
    editor?.commands?.toggleHeading({ level });
  };

  return (
    <div className="select-none cursor-pointer w-[40px] p-[5px] text-center relative group">
      <svg
        width="20"
        height="16"
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M22 0H2C1.46957 0 0.960859 0.210714 0.585786 0.585786C0.210714 0.960859 0 1.46957 0 2V22C0 22.5304 0.210714 23.0391 0.585786 23.4142C0.960859 23.7893 1.46957 24 2 24H22C22.5304 24 23.0391 23.7893 23.4142 23.4142C23.7893 23.0391 24 22.5304 24 22V2C24 1.46957 23.7893 0.960859 23.4142 0.585786C23.0391 0.210714 22.5304 0 22 0ZM19 18C19 18.2652 18.8946 18.5196 18.7071 18.7071C18.5196 18.8946 18.2652 19 18 19C17.7348 19 17.4804 18.8946 17.2929 18.7071C17.1054 18.5196 17 18.2652 17 18V13H7V18C7 18.2652 6.89464 18.5196 6.70711 18.7071C6.51957 18.8946 6.26522 19 6 19C5.73478 19 5.48043 18.8946 5.29289 18.7071C5.10536 18.5196 5 18.2652 5 18V6C5 5.73478 5.10536 5.48043 5.29289 5.29289C5.48043 5.10536 5.73478 5 6 5C6.26522 5 6.51957 5.10536 6.70711 5.29289C6.89464 5.48043 7 5.73478 7 6V11H17V6C17 5.73478 17.1054 5.48043 17.2929 5.29289C17.4804 5.10536 17.7348 5 18 5C18.2652 5 18.5196 5.10536 18.7071 5.29289C18.8946 5.48043 19 5.73478 19 6V18Z"
          fill="currentColor"
        />
      </svg>
      <div className="flex p-[10px] gap-[5px] -left-[50%] opacity-0 pointer-events-none group-hover:pointer-events-auto group-hover:opacity-100 bg-customColor55 border border-customColor3 z-[100] absolute transition-all">
        <div onClick={setHeading(1)}>
          <svg
            width="20"
            height="16"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M22 0H2C1.46957 0 0.960859 0.210714 0.585786 0.585786C0.210714 0.960859 0 1.46957 0 2V22C0 22.5304 0.210714 23.0391 0.585786 23.4142C0.960859 23.7893 1.46957 24 2 24H22C22.5304 24 23.0391 23.7893 23.4142 23.4142C23.7893 23.0391 24 22.5304 24 22V2C24 1.46957 23.7893 0.960859 23.4142 0.585786C23.0391 0.210714 22.5304 0 22 0ZM14 16C14 16.2652 13.8946 16.5196 13.7071 16.7071C13.5196 16.8946 13.2652 17 13 17C12.7348 17 12.4804 16.8946 12.2929 16.7071C12.1054 16.5196 12 16.2652 12 16V12H5V16C5 16.2652 4.89464 16.5196 4.70711 16.7071C4.51957 16.8946 4.26522 17 4 17C3.73478 17 3.48043 16.8946 3.29289 16.7071C3.10536 16.5196 3 16.2652 3 16V6C3 5.73478 3.10536 5.48043 3.29289 5.29289C3.48043 5.10536 3.73478 5 4 5C4.26522 5 4.51957 5.10536 4.70711 5.29289C4.89464 5.48043 5 5.73478 5 6V10H12V6C12 5.73478 12.1054 5.48043 12.2929 5.29289C12.4804 5.10536 12.7348 5 13 5C13.2652 5 13.5196 5.10536 13.7071 5.29289C13.8946 5.48043 14 5.73478 14 6V16ZM21 18C21 18.2652 20.8946 18.5196 20.7071 18.7071C20.5196 18.8946 20.2652 19 20 19C19.7348 19 19.4804 18.8946 19.2929 18.7071C19.1054 18.5196 19 18.2652 19 18V9.875L17.555 10.8387C17.4457 10.9116 17.3231 10.9623 17.1942 10.9878C17.0653 11.0133 16.9326 11.0131 16.8038 10.9874C16.6749 10.9616 16.5524 10.9107 16.4433 10.8376C16.3341 10.7645 16.2404 10.6706 16.1675 10.5612C16.0946 10.4519 16.044 10.3293 16.0185 10.2004C15.993 10.0715 15.9931 9.93887 16.0189 9.81003C16.0447 9.68119 16.0956 9.55868 16.1687 9.44951C16.2418 9.34034 16.3357 9.24663 16.445 9.17375L19.445 7.17375C19.5952 7.07354 19.7697 7.01586 19.9501 7.00684C20.1304 6.99782 20.3098 7.03779 20.4692 7.12251C20.6287 7.20723 20.7622 7.33354 20.8557 7.48804C20.9491 7.64253 20.999 7.81945 21 8V18Z"
              fill="currentColor"
            />
          </svg>
        </div>
        <div onClick={setHeading(2)}>
          <svg
            width="20"
            height="16"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M22 0H2C1.46957 0 0.960859 0.210714 0.585786 0.585786C0.210714 0.960859 0 1.46957 0 2V22C0 22.5304 0.210714 23.0391 0.585786 23.4142C0.960859 23.7893 1.46957 24 2 24H22C22.5304 24 23.0391 23.7893 23.4142 23.4142C23.7893 23.0391 24 22.5304 24 22V2C24 1.46957 23.7893 0.960859 23.4142 0.585786C23.0391 0.210714 22.5304 0 22 0ZM12 16C12 16.2652 11.8946 16.5196 11.7071 16.7071C11.5196 16.8946 11.2652 17 11 17C10.7348 17 10.4804 16.8946 10.2929 16.7071C10.1054 16.5196 10 16.2652 10 16V12H5V16C5 16.2652 4.89464 16.5196 4.70711 16.7071C4.51957 16.8946 4.26522 17 4 17C3.73478 17 3.48043 16.8946 3.29289 16.7071C3.10536 16.5196 3 16.2652 3 16V6C3 5.73478 3.10536 5.48043 3.29289 5.29289C3.48043 5.10536 3.73478 5 4 5C4.26522 5 4.51957 5.10536 4.70711 5.29289C4.89464 5.48043 5 5.73478 5 6V10H10V6C10 5.73478 10.1054 5.48043 10.2929 5.29289C10.4804 5.10536 10.7348 5 11 5C11.2652 5 11.5196 5.10536 11.7071 5.29289C11.8946 5.48043 12 5.73478 12 6V16ZM20 19H15C14.8143 19 14.6322 18.9483 14.4743 18.8507C14.3163 18.753 14.1886 18.6133 14.1056 18.4472C14.0225 18.2811 13.9874 18.0952 14.004 17.9102C14.0207 17.7252 14.0886 17.5486 14.2 17.4L18.7 11.4C18.8229 11.2432 18.9133 11.0634 18.966 10.8713C19.0187 10.6791 19.0325 10.4784 19.0068 10.2808C18.981 10.0832 18.9161 9.89275 18.816 9.72052C18.7158 9.54829 18.5823 9.39774 18.4233 9.27768C18.2642 9.15761 18.0829 9.07042 17.8898 9.02121C17.6968 8.972 17.4958 8.96175 17.2987 8.99106C17.1016 9.02037 16.9124 9.08865 16.742 9.19191C16.5716 9.29518 16.4234 9.43136 16.3062 9.5925C16.1576 9.76796 16.0477 9.97286 15.9837 10.1938C15.9588 10.3235 15.9083 10.4471 15.8353 10.5572C15.7623 10.6674 15.6682 10.762 15.5584 10.8355C15.4486 10.9091 15.3253 10.9601 15.1956 10.9858C15.066 11.0114 14.9325 11.011 14.803 10.9848C14.6735 10.9585 14.5505 10.9068 14.441 10.8327C14.3316 10.7586 14.238 10.6636 14.1655 10.553C14.093 10.4425 14.0432 10.3187 14.0189 10.1888C13.9945 10.0589 13.9962 9.9255 14.0238 9.79625C14.129 9.27928 14.349 8.79256 14.6676 8.37206C14.9862 7.95155 15.3952 7.60801 15.8644 7.36682C16.3336 7.12562 16.851 6.99294 17.3784 6.97858C17.9058 6.96422 18.4296 7.06854 18.9113 7.28384C19.3929 7.49914 19.82 7.8199 20.161 8.22245C20.502 8.62499 20.7482 9.09901 20.8814 9.60948C21.0146 10.12 21.0314 10.6538 20.9306 11.1717C20.8297 11.6895 20.6138 12.1781 20.2987 12.6012L17 17H20C20.2652 17 20.5196 17.1054 20.7071 17.2929C20.8946 17.4804 21 17.7348 21 18C21 18.2652 20.8946 18.5196 20.7071 18.7071C20.5196 18.8946 20.2652 19 20 19Z"
              fill="currentColor"
            />
          </svg>
        </div>
        <div onClick={setHeading(3)}>
          <svg
            width="20"
            height="16"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M22 0H2C1.46957 0 0.960859 0.210714 0.585786 0.585786C0.210714 0.960859 0 1.46957 0 2V22C0 22.5304 0.210714 23.0391 0.585786 23.4142C0.960859 23.7893 1.46957 24 2 24H22C22.5304 24 23.0391 23.7893 23.4142 23.4142C23.7893 23.0391 24 22.5304 24 22V2C24 1.46957 23.7893 0.960859 23.4142 0.585786C23.0391 0.210714 22.5304 0 22 0ZM12 16C12 16.2652 11.8946 16.5196 11.7071 16.7071C11.5196 16.8946 11.2652 17 11 17C10.7348 17 10.4804 16.8946 10.2929 16.7071C10.1054 16.5196 10 16.2652 10 16V12H5V16C5 16.2652 4.89464 16.5196 4.70711 16.7071C4.51957 16.8946 4.26522 17 4 17C3.73478 17 3.48043 16.8946 3.29289 16.7071C3.10536 16.5196 3 16.2652 3 16V6C3 5.73478 3.10536 5.48043 3.29289 5.29289C3.48043 5.10536 3.73478 5 4 5C4.26522 5 4.51957 5.10536 4.70711 5.29289C4.89464 5.48043 5 5.73478 5 6V10H10V6C10 5.73478 10.1054 5.48043 10.2929 5.29289C10.4804 5.10536 10.7348 5 11 5C11.2652 5 11.5196 5.10536 11.7071 5.29289C11.8946 5.48043 12 5.73478 12 6V16ZM17 19C16.0158 19.0002 15.0661 18.6374 14.3325 17.9813C14.1349 17.8042 14.0157 17.5559 14.0012 17.291C13.9867 17.0262 14.078 16.7663 14.255 16.5688C14.432 16.3712 14.6803 16.252 14.9452 16.2375C15.2101 16.2229 15.4699 16.3142 15.6675 16.4912C15.9092 16.7073 16.1998 16.8613 16.5143 16.9401C16.8287 17.0188 17.1576 17.02 17.4726 16.9434C17.7876 16.8669 18.0793 16.7149 18.3225 16.5006C18.5657 16.2862 18.7532 16.016 18.8688 15.7132C18.9844 15.4103 19.0246 15.0839 18.986 14.762C18.9474 14.4402 18.8312 14.1325 18.6473 13.8655C18.4635 13.5985 18.2174 13.3803 17.9305 13.2295C17.6435 13.0787 17.3242 13 17 13C16.8143 13 16.6322 12.9483 16.4743 12.8507C16.3163 12.753 16.1886 12.6133 16.1056 12.4472C16.0225 12.2811 15.9874 12.0952 16.004 11.9102C16.0207 11.7252 16.0886 11.5486 16.2 11.4L18 9H15C14.7348 9 14.4804 8.89464 14.2929 8.70711C14.1054 8.51957 14 8.26522 14 8C14 7.73478 14.1054 7.48043 14.2929 7.29289C14.4804 7.10536 14.7348 7 15 7H20C20.1857 7 20.3678 7.05171 20.5257 7.14935C20.6837 7.24698 20.8114 7.38668 20.8944 7.55279C20.9775 7.71889 21.0126 7.90484 20.996 8.08981C20.9793 8.27477 20.9114 8.45143 20.8 8.6L18.7113 11.385C19.5321 11.7738 20.1962 12.4304 20.5943 13.2468C20.9924 14.0632 21.1008 14.9908 20.9017 15.877C20.7025 16.7632 20.2077 17.5552 19.4986 18.1228C18.7895 18.6904 17.9083 18.9998 17 19Z"
              fill="currentColor"
            />
          </svg>
        </div>
      </div>
    </div>
  );
};
