version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: socialsync-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: socialsync
      POSTGRES_USER: socialsync
      POSTGRES_PASSWORD: ${DB_PASSWORD:-socialsync_dev_password}
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    ports:
      - "${DB_PORT:-5432}:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./Backend/src/main/resources/db/init:/docker-entrypoint-initdb.d
    networks:
      - socialsync-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U socialsync -d socialsync"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: socialsync-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-redis_dev_password}
    ports:
      - "${REDIS_PORT:-6379}:6379"
    volumes:
      - redis_data:/data
    networks:
      - socialsync-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s

  # Spring Boot Backend
  backend:
    build:
      context: ./Backend
      dockerfile: Dockerfile
      target: development
    container_name: socialsync-backend
    restart: unless-stopped
    environment:
      SPRING_PROFILES_ACTIVE: ${SPRING_PROFILES_ACTIVE:-dev}
      DB_HOST: postgres
      DB_PORT: 5432
      DB_NAME: socialsync
      DB_USERNAME: socialsync
      DB_PASSWORD: ${DB_PASSWORD:-socialsync_dev_password}
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_PASSWORD: ${REDIS_PASSWORD:-redis_dev_password}
      JWT_SECRET: ${JWT_SECRET:-dev_jwt_secret_key_change_in_production}
      SERVER_PORT: 8080
    ports:
      - "${BACKEND_PORT:-8080}:8080"
    volumes:
      - ./Backend:/app
      - backend_cache:/root/.m2
    networks:
      - socialsync-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # React Frontend
  frontend:
    build:
      context: ./Frontend
      dockerfile: Dockerfile
      target: development
    container_name: socialsync-frontend
    restart: unless-stopped
    environment:
      NODE_ENV: ${NODE_ENV:-development}
      REACT_APP_API_URL: ${REACT_APP_API_URL:-http://localhost:8080/api/v1}
      REACT_APP_ENVIRONMENT: ${REACT_APP_ENVIRONMENT:-development}
    ports:
      - "${FRONTEND_PORT:-3000}:3000"
    volumes:
      - ./Frontend:/app
      - frontend_node_modules:/app/node_modules
    networks:
      - socialsync-network
    depends_on:
      - backend
    stdin_open: true
    tty: true

  # pgAdmin for Database Management (Development Only)
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: socialsync-pgadmin
    restart: unless-stopped
    environment:
      PGADMIN_DEFAULT_EMAIL: ${PGADMIN_EMAIL:-<EMAIL>}
      PGADMIN_DEFAULT_PASSWORD: ${PGADMIN_PASSWORD:-admin}
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - "${PGADMIN_PORT:-5050}:80"
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    networks:
      - socialsync-network
    depends_on:
      - postgres
    profiles:
      - dev

  # Redis Commander for Redis Management (Development Only)
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: socialsync-redis-commander
    restart: unless-stopped
    environment:
      REDIS_HOSTS: local:redis:6379:0:${REDIS_PASSWORD:-redis_dev_password}
      HTTP_USER: ${REDIS_COMMANDER_USER:-admin}
      HTTP_PASSWORD: ${REDIS_COMMANDER_PASSWORD:-admin}
    ports:
      - "${REDIS_COMMANDER_PORT:-8081}:8081"
    networks:
      - socialsync-network
    depends_on:
      - redis
    profiles:
      - dev

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  backend_cache:
    driver: local
  frontend_node_modules:
    driver: local
  pgadmin_data:
    driver: local

networks:
  socialsync-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
