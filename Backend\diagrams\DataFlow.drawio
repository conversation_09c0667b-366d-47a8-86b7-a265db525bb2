<mxfile host="app.diagrams.net" modified="2025-08-21T00:00:00.000Z" agent="Augment" version="20.8.16">
  <diagram name="Data Flow">
    <mxGraphModel dx="1800" dy="1200" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="2400" pageHeight="1600" math="0" shadow="0">
      <root>
        <mxCell id="0"/>
        <mxCell id="1" parent="0"/>

        <!-- Swimlanes for stages -->
        <mxCell id="sl_users" value="Users / Frontend" style="swimlane;horizontal=0;startSize=28;rounded=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="40" y="40" width="400" height="1320" as="geometry"/>
        </mxCell>
        <mxCell id="sl_gateway" value="API Gateway" style="swimlane;horizontal=0;startSize=28;rounded=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="460" y="40" width="380" height="1320" as="geometry"/>
        </mxCell>
        <mxCell id="sl_services" value="Services" style="swimlane;horizontal=0;startSize=28;rounded=1;fillColor=#f5f5f5;strokeColor=#b3b3b3;" vertex="1" parent="1">
          <mxGeometry x="860" y="40" width="720" height="1320" as="geometry"/>
        </mxCell>
        <mxCell id="sl_data" value="Data / MQ / External" style="swimlane;horizontal=0;startSize=28;rounded=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="1600" y="40" width="720" height="1320" as="geometry"/>
        </mxCell>

        <!-- Auth flow -->
        <mxCell id="u_login" value="Login/Register" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="sl_users">
          <mxGeometry x="20" y="40" width="180" height="60" as="geometry"/>
        </mxCell>
        <mxCell id="g_auth" value="JWT issuance" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="sl_gateway">
          <mxGeometry x="20" y="40" width="200" height="60" as="geometry"/>
        </mxCell>
        <mxCell id="s_auth" value="AuthService" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="sl_services">
          <mxGeometry x="20" y="40" width="200" height="60" as="geometry"/>
        </mxCell>
        <mxCell id="d_user" value="users" style="shape=cylinder;whiteSpace=wrap;html=1;" vertex="1" parent="sl_data">
          <mxGeometry x="20" y="40" width="160" height="60" as="geometry"/>
        </mxCell>

        <mxCell id="e_auth1" edge="1" parent="1" source="u_login" target="g_auth" style="endArrow=block;"/>
        <mxCell id="e_auth2" edge="1" parent="1" source="g_auth" target="s_auth" style="endArrow=block;"/>
        <mxCell id="e_auth3" edge="1" parent="1" source="s_auth" target="d_user" style="endArrow=block;"/>

        <!-- OAuth connect flow -->
        <mxCell id="u_connect" value="Connect Social Account" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="sl_users">
          <mxGeometry x="20" y="140" width="220" height="60" as="geometry"/>
        </mxCell>
        <mxCell id="g_oauth" value="Redirect to Provider (state/PKCE)" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="sl_gateway">
          <mxGeometry x="20" y="140" width="260" height="60" as="geometry"/>
        </mxCell>
        <mxCell id="s_integ" value="IntegrationService\nProvider Registry" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="sl_services">
          <mxGeometry x="20" y="140" width="260" height="80" as="geometry"/>
        </mxCell>
        <mxCell id="d_integ" value="integration(s)" style="shape=cylinder;whiteSpace=wrap;html=1;" vertex="1" parent="sl_data">
          <mxGeometry x="20" y="140" width="180" height="70" as="geometry"/>
        </mxCell>
        <mxCell id="ext_social" value="Social APIs" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="sl_data">
          <mxGeometry x="220" y="140" width="180" height="60" as="geometry"/>
        </mxCell>

        <mxCell id="e_oauth1" edge="1" parent="1" source="u_connect" target="g_oauth" style="endArrow=block;"/>
        <mxCell id="e_oauth2" edge="1" parent="1" source="g_oauth" target="s_integ" style="endArrow=block;"/>
        <mxCell id="e_oauth3" edge="1" parent="1" source="s_integ" target="ext_social" style="endArrow=block;"/>
        <mxCell id="e_oauth4" edge="1" parent="1" source="s_integ" target="d_integ" style="endArrow=block;"/>

        <!-- Create Post & Scheduling -->
        <mxCell id="u_create" value="Create Post (content + media + settings)" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="sl_users">
          <mxGeometry x="20" y="260" width="320" height="60" as="geometry"/>
        </mxCell>
        <mxCell id="g_posts" value="/posts" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="sl_gateway">
          <mxGeometry x="20" y="260" width="200" height="60" as="geometry"/>
        </mxCell>
        <mxCell id="s_content" value="ContentService" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="sl_services">
          <mxGeometry x="20" y="260" width="200" height="60" as="geometry"/>
        </mxCell>
        <mxCell id="d_post" value="posts" style="shape=cylinder;whiteSpace=wrap;html=1;" vertex="1" parent="sl_data">
          <mxGeometry x="20" y="260" width="160" height="60" as="geometry"/>
        </mxCell>

        <mxCell id="u_schedule" value="Schedule Post" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="sl_users">
          <mxGeometry x="20" y="340" width="200" height="50" as="geometry"/>
        </mxCell>
        <mxCell id="g_schedule" value="/posts/{id}/schedule" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="sl_gateway">
          <mxGeometry x="20" y="340" width="220" height="50" as="geometry"/>
        </mxCell>
        <mxCell id="s_sched" value="SchedulingService (Quartz)" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="sl_services">
          <mxGeometry x="20" y="340" width="260" height="60" as="geometry"/>
        </mxCell>
        <mxCell id="mq_jobs" value="Jobs Queue" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#cc9966;" vertex="1" parent="sl_data">
          <mxGeometry x="20" y="340" width="180" height="60" as="geometry"/>
        </mxCell>

        <mxCell id="e_post1" edge="1" parent="1" source="u_create" target="g_posts" style="endArrow=block;"/>
        <mxCell id="e_post2" edge="1" parent="1" source="g_posts" target="s_content" style="endArrow=block;"/>
        <mxCell id="e_post3" edge="1" parent="1" source="s_content" target="d_post" style="endArrow=block;"/>

        <mxCell id="e_sched1" edge="1" parent="1" source="u_schedule" target="g_schedule" style="endArrow=block;"/>
        <mxCell id="e_sched2" edge="1" parent="1" source="g_schedule" target="s_sched" style="endArrow=block;"/>
        <mxCell id="e_sched3" edge="1" parent="1" source="s_sched" target="mq_jobs" style="endArrow=block;"/>

        <!-- Publishing pipeline -->
        <mxCell id="s_worker" value="Worker: Publish Job\n- Load post & integration\n- Media upload/preprocess\n- Provider post()\n- Retry/backoff" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="sl_services">
          <mxGeometry x="20" y="440" width="320" height="120" as="geometry"/>
        </mxCell>
        <mxCell id="e_pub1" edge="1" parent="1" source="mq_jobs" target="s_worker" style="endArrow=block;"/>

        <mxCell id="d_media" value="media_files (CDN)" style="shape=cylinder;whiteSpace=wrap;html=1;" vertex="1" parent="sl_data">
          <mxGeometry x="20" y="440" width="180" height="60" as="geometry"/>
        </mxCell>
        <mxCell id="e_pub2" edge="1" parent="1" source="s_worker" target="d_media" style="endArrow=block;dashed=1;"/>

        <mxCell id="e_pub3" edge="1" parent="1" source="s_worker" target="ext_social" style="endArrow=block;"/>
        <mxCell id="d_postplat" value="post_platforms" style="shape=cylinder;whiteSpace=wrap;html=1;" vertex="1" parent="sl_data">
          <mxGeometry x="220" y="440" width="180" height="60" as="geometry"/>
        </mxCell>
        <mxCell id="e_pub4" edge="1" parent="1" source="s_worker" target="d_postplat" style="endArrow=block;"/>

        <!-- Error handling -->
        <mxCell id="d_errors" value="errors" style="shape=cylinder;whiteSpace=wrap;html=1;" vertex="1" parent="sl_data">
          <mxGeometry x="420" y="440" width="160" height="60" as="geometry"/>
        </mxCell>
        <mxCell id="e_err1" edge="1" parent="1" source="s_worker" target="d_errors" style="endArrow=block;dashed=1;"/>

        <!-- Analytics collection -->
        <mxCell id="u_analytics" value="View Analytics" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="sl_users">
          <mxGeometry x="20" y="620" width="180" height="50" as="geometry"/>
        </mxCell>
        <mxCell id="g_analytics" value="/analytics" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="sl_gateway">
          <mxGeometry x="20" y="620" width="160" height="50" as="geometry"/>
        </mxCell>
        <mxCell id="s_analytics" value="AnalyticsService\n- normalize/store" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="sl_services">
          <mxGeometry x="20" y="620" width="240" height="70" as="geometry"/>
        </mxCell>
        <mxCell id="d_analytics" value="analytics" style="shape=cylinder;whiteSpace=wrap;html=1;" vertex="1" parent="sl_data">
          <mxGeometry x="20" y="620" width="160" height="60" as="geometry"/>
        </mxCell>
        <mxCell id="e_an1" edge="1" parent="1" source="u_analytics" target="g_analytics" style="endArrow=block;"/>
        <mxCell id="e_an2" edge="1" parent="1" source="g_analytics" target="s_analytics" style="endArrow=block;"/>
        <mxCell id="e_an3" edge="1" parent="1" source="s_analytics" target="d_analytics" style="endArrow=block;"/>

        <mxCell id="s_an_job" value="Analytics Worker\n- fetch per provider\n- backoff & cache" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="sl_services">
          <mxGeometry x="20" y="710" width="260" height="90" as="geometry"/>
        </mxCell>
        <mxCell id="e_an4" edge="1" parent="1" source="mq_jobs" target="s_an_job" style="endArrow=block;dashed=1;"/>
        <mxCell id="e_an5" edge="1" parent="1" source="s_an_job" target="ext_social" style="endArrow=block;"/>
        <mxCell id="e_an6" edge="1" parent="1" source="s_an_job" target="d_analytics" style="endArrow=block;"/>

        <!-- OAuth callback data flow (return) -->
        <mxCell id="cb_note" value="OAuth callback → code exchange → store tokens (encrypted)" style="text;whiteSpace=wrap;html=1;align=left;" vertex="1" parent="sl_services">
          <mxGeometry x="20" y="850" width="660" height="50" as="geometry"/>
        </mxCell>

        <!-- Normalization pipeline note -->
        <mxCell id="norm_note" value="Normalization: raw platform payload → canonical metrics + raw JSON storage" style="text;whiteSpace=wrap;html=1;align=left;" vertex="1" parent="sl_data">
          <mxGeometry x="20" y="710" width="480" height="50" as="geometry"/>
        </mxCell>

      </root>
    </mxGraphModel>
  </diagram>
</mxfile>

