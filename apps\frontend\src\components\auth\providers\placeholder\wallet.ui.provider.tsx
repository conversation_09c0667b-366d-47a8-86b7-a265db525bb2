import { FC } from 'react';
import { useT } from '@gitroom/react/translation/get.transation.service.client';

export const WalletUiProvider: FC = () => {
  const t = useT();
  return (
    <div
      className={`cursor-pointer bg-[#0b2181] h-[44px] rounded-[4px] flex justify-center items-center text-white gap-[7px]`}
    >
      <svg
        width="18"
        viewBox="0 0 25 22"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M23 4H3C2.73478 4 2.48043 3.89464 2.29289 3.70711C2.10536 3.51957 2 3.26522 2 3C2 2.73478 2.10536 2.48043 2.29289 2.29289C2.48043 2.10536 2.73478 2 3 2H20C20.2652 2 20.5196 1.89464 20.7071 1.70711C20.8946 1.51957 21 1.26522 21 1C21 0.734784 20.8946 0.48043 20.7071 0.292893C20.5196 0.105357 20.2652 0 20 0H3C2.20435 0 1.44129 0.316071 0.87868 0.87868C0.316071 1.44129 0 2.20435 0 3V19C0 19.7956 0.316071 20.5587 0.87868 21.1213C1.44129 21.6839 2.20435 22 3 22H23C23.5304 22 24.0391 21.7893 24.4142 21.4142C24.7893 21.0391 25 20.5304 25 20V6C25 5.46957 24.7893 4.96086 24.4142 4.58579C24.0391 4.21071 23.5304 4 23 4ZM18.5 14C18.2033 14 17.9133 13.912 17.6666 13.7472C17.42 13.5824 17.2277 13.3481 17.1142 13.074C17.0007 12.7999 16.9709 12.4983 17.0288 12.2074C17.0867 11.9164 17.2296 11.6491 17.4393 11.4393C17.6491 11.2296 17.9164 11.0867 18.2074 11.0288C18.4983 10.9709 18.7999 11.0007 19.074 11.1142C19.3481 11.2277 19.5824 11.42 19.7472 11.6666C19.912 11.9133 20 12.2033 20 12.5C20 12.8978 19.842 13.2794 19.5607 13.5607C19.2794 13.842 18.8978 14 18.5 14Z"
          fill="#fff"
        />
      </svg>
      {t('continue_with_your_wallet', 'Continue with your Wallet')}
    </div>
  );
};
