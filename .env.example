# SocialSync Development Environment Configuration
# Copy this file to .env and update the values as needed

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
DB_PASSWORD=socialsync_dev_password
DB_PORT=5432

# =============================================================================
# REDIS CONFIGURATION
# =============================================================================
REDIS_PASSWORD=redis_dev_password
REDIS_PORT=6379

# =============================================================================
# BACKEND CONFIGURATION
# =============================================================================
SPRING_PROFILES_ACTIVE=dev
BACKEND_PORT=8080
JWT_SECRET=dev_jwt_secret_key_change_in_production_minimum_256_bits

# =============================================================================
# FRONTEND CONFIGURATION
# =============================================================================
NODE_ENV=development
FRONTEND_PORT=3000
REACT_APP_API_URL=http://localhost:8080/api/v1
REACT_APP_ENVIRONMENT=development

# =============================================================================
# DEVELOPMENT TOOLS (Optional - only loaded with --profile dev)
# =============================================================================
PGADMIN_EMAIL=<EMAIL>
PGADMIN_PASSWORD=admin
PGADMIN_PORT=5050

REDIS_COMMANDER_USER=admin
REDIS_COMMANDER_PASSWORD=admin
REDIS_COMMANDER_PORT=8081

# =============================================================================
# OAUTH2 CONFIGURATION (To be configured later)
# =============================================================================
# Facebook
FACEBOOK_CLIENT_ID=
FACEBOOK_CLIENT_SECRET=

# Instagram
INSTAGRAM_CLIENT_ID=
INSTAGRAM_CLIENT_SECRET=

# Twitter/X
TWITTER_CLIENT_ID=
TWITTER_CLIENT_SECRET=

# YouTube
YOUTUBE_CLIENT_ID=
YOUTUBE_CLIENT_SECRET=

# Pinterest
PINTEREST_CLIENT_ID=
PINTEREST_CLIENT_SECRET=

# =============================================================================
# VIDEO PROCESSING APIs (To be configured later)
# =============================================================================
PLAINLY_API_KEY=
CREATOMATE_API_KEY=

# =============================================================================
# MONITORING AND LOGGING (To be configured later)
# =============================================================================
SENTRY_DSN=
LOG_LEVEL=INFO
